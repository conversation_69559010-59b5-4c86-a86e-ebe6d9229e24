import { librarySecondaryEntryPointGenerator } from '@nx/angular/generators';
import {
  GeneratorCallback,
  Tree,
  formatFiles,
  generateFiles,
  getProjects,
  joinPathFragments,
  logger,
  names,
  readProjectConfiguration,
  runTasksInSerial,
} from '@nx/devkit';
import { execSync } from 'child_process';
import * as fs from 'fs';
import { printChanges } from 'nx/src/generators/tree';
import * as os from 'os';
import { join } from 'path';
import { SwaggerSpec } from './models/swagger-spec';
import { SwaggerGeneratorSchema } from './schema';

export default async function swaggerGenerator(
  tree: Tree,
  options: SwaggerGeneratorSchema,
): Promise<GeneratorCallback> {
  const { filePath, projectName, requiredDemoController } = options;
  const isDryRun = process.env.NX_DRY_RUN !== 'false';
  const tempDir = fs.mkdtempSync(join(os.tmpdir(), 'swagger-gen-'));
  // Validate inputs
  if (!tree.exists(filePath)) {
    throw new Error(`Swagger JSON file not found at: ${filePath}`);
  }

  // Get project configuration
  const projects = getProjects(tree);
  if (!projects.has(projectName)) {
    const availableProjects = Array.from(projects.keys()).filter(
      (name) => name.includes('swagger') || name === 'swagger-generator',
    );
    throw new Error(
      `Project "${projectName}" not found in workspace. ` +
        `Available swagger projects: ${availableProjects.join(', ')}`,
    );
  }

  const projectConfig = readProjectConfiguration(tree, projectName);
  const projectRoot = projectConfig.root;

  // Read and parse swagger.json
  const swaggerContent = tree.read(filePath, 'utf-8');
  if (!swaggerContent) {
    throw new Error(`Could not read swagger.json file at: ${filePath}`);
  }

  const swaggerSpec: SwaggerSpec = JSON.parse(swaggerContent);

  // Extract microservice name from servers array
  const microserviceName = extractMicroserviceName(swaggerSpec);

  if (!microserviceName) {
    throw new Error(
      `Could not extract microservice name from swagger specification`,
    );
  }

  // Check for single-cluster-demo-controller string only for allowed microservices
  if (requiredDemoController.includes(microserviceName)) {
    if (!swaggerContent.includes('single-cluster-demo-controller')) {
      const warningMessage =
        'The ' +
        filePath +
        ' file does not contain "single-cluster-demo-controller".';
      throw new Error(warningMessage);
    }
  }

  // Define paths
  const microserviceRoot = joinPathFragments(projectRoot, microserviceName);
  const libPath = joinPathFragments(microserviceRoot, 'src', 'lib');

  // Check if entry point exists
  const entryPointExists = tree.exists(microserviceRoot);

  const tasks: GeneratorCallback[] = [];

  // Create or update entry point
  if (!entryPointExists) {
    logger.info(`Creating new entry point for: ${microserviceName}`);
    const entryPointTask = await createSecondaryEntryPoint(
      tree,
      projectName,
      microserviceName,
    );
    if (entryPointTask) {
      tasks.push(entryPointTask);
    }
  } else {
    logger.info(`Updating existing entry point for: ${microserviceName}`);
  }

  // Generate files using ng-swagger-gen
  await generateSwaggerFiles(
    tree,
    filePath,
    libPath,
    microserviceName,
    isDryRun,
    tempDir,
    entryPointExists,
  );

  if (!entryPointExists) {
    // Create/update index.ts
    createIndexFile(tree, microserviceRoot);
  }

  // Format files
  await formatFiles(tree);

  return runTasksInSerial(...tasks);
}

function extractMicroserviceName(swaggerSpec: SwaggerSpec): string | null {
  // Try to extract from servers array first
  if (swaggerSpec.servers && swaggerSpec.servers.length > 0) {
    for (const server of swaggerSpec.servers) {
      if (server.url && server.url.trim()) {
        return extractMicroserviceNameFromUrl(server.url);
      }
    }
  }

  // Fallback to extracting from info.title
  if (swaggerSpec.info && swaggerSpec.info.title) {
    return extractMicroserviceNameFromTitle(swaggerSpec.info.title);
  }

  return null;
}

function extractMicroserviceNameFromUrl(url: string): string | null {
  const cleanUrl = url.replace('/api/', '');
  const dashIndex = cleanUrl.indexOf('-');
  if (dashIndex === -1) {
    return names(cleanUrl).fileName;
  }

  const nameWithoutPrefix = cleanUrl.substring(dashIndex + 1);
  return names(nameWithoutPrefix).fileName;
}

function extractMicroserviceNameFromTitle(title: string): string | null {
  // Strip prefix (separated by dash) and return the remaining part
  const dashIndex = title.indexOf('-');
  if (dashIndex === -1) {
    return names(title).fileName;
  }

  const nameWithoutPrefix = title.substring(dashIndex + 1);
  return names(nameWithoutPrefix).fileName;
}

async function createSecondaryEntryPoint(
  tree: Tree,
  projectName: string,
  microserviceName: string,
): Promise<GeneratorCallback | undefined> {
  // Use the official Nx Angular generator for secondary entry points
  await librarySecondaryEntryPointGenerator(tree, {
    name: microserviceName,
    library: projectName,
    skipModule: true, // We don't need a module since we're generating API clients
  });
  return undefined;
}

async function generateSwaggerFiles(
  tree: Tree,
  filePath: string,
  libPath: string,
  microserviceName: string,
  isDryRun: boolean,
  tempDir: string,
  entryPointExists: boolean,
) {
  logger.info(`Generating OpenAPI files for: ${microserviceName}`);

  // Convert Tree path to absolute path for ng-swagger-gen
  const workspaceRoot = tree.root;
  const absoluteSwaggerPath = join(workspaceRoot, filePath);

  try {
    // Run ng-openapi-gen (supports OpenAPI 3.x)
    const command = `npx ng-openapi-gen --input "${absoluteSwaggerPath}" --output "${tempDir}" --useTempDir ${isDryRun} --silent ${!isDryRun}`;

    execSync(command, {
      stdio: 'inherit',
      cwd: workspaceRoot,
    });
    if (!isDryRun) {
      syncSwaggerFilesIntoTree(tree, tempDir, libPath);

      if (entryPointExists) {
        const changes = tree.listChanges();
        printChanges(changes);
      }
    }
  } catch (error) {
    throw new Error(error);
  }
}

function syncSwaggerFilesIntoTree(
  tree: Tree,
  swaggerOutputDir: string,
  targetDirInTree: string,
) {
  const entries = fs.readdirSync(swaggerOutputDir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = join(swaggerOutputDir, entry.name);

    if (entry.isFile()) {
      const content = fs.readFileSync(fullPath, 'utf-8');
      const targetPath = join(targetDirInTree, entry.name);
      tree.write(targetPath, content); // ✅ Now tracked by Nx
    } else if (entry.isDirectory()) {
      // Recursively sync subdirectories
      syncSwaggerFilesIntoTree(
        tree,
        fullPath,
        join(targetDirInTree, entry.name),
      );
    }
  }
}

function createIndexFile(tree: Tree, microserviceRoot: string) {
  generateFiles(
    tree,
    joinPathFragments(__dirname, 'files'),
    microserviceRoot,
    {},
  );
}
