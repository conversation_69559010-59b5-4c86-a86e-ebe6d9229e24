import { Tree, readProjectConfiguration } from '@nx/devkit';
import { createTreeWithEmptyWorkspace } from '@nx/devkit/testing';

import { swaggerGeneratorGenerator } from './generator';
import { SwaggerGeneratorGeneratorSchema } from './schema';

describe('swagger-generator generator', () => {
  let tree: Tree;
  const options: SwaggerGeneratorGeneratorSchema = { name: 'test' };

  beforeEach(() => {
    tree = createTreeWithEmptyWorkspace();
  });

  it('should run successfully', async () => {
    await swaggerGeneratorGenerator(tree, options);
    const config = readProjectConfiguration(tree, 'test');
    expect(config).toBeDefined();
  });
});
