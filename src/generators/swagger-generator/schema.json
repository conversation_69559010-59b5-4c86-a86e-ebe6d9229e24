{"$schema": "http://json-schema.org/draft-07/schema", "cli": "nx", "id": "swagger-generator", "type": "object", "title": "Swagger Generator", "description": "Generate Angular services and types from Swagger JSON for microservices", "properties": {"filePath": {"type": "string", "description": "Path to the Swagger JSON file", "default": "api-docs.json", "x-prompt": "What is the path to the Swagger JSON file?", "alias": "f"}, "branchName": {"type": "string", "description": "Name of the branch to download the Swagger JSON file from", "alias": "b", "x-priority": "important"}, "projectName": {"type": "string", "description": "Name of the swagger-generator project", "default": "swagger-generator", "x-prompt": "Which project should be used for the swagger generator?", "alias": "p"}, "requiredDemoController": {"type": "array", "description": "A list of microservices requiring the `SingleClusterDemoController`. For more information, see https://neoshare.atlassian.net/wiki/x/GgDlO", "default": ["authorization-server", "business-case-manager", "company", "financing-details", "portal"], "items": {"type": "string"}, "alias": "d"}, "selectService": {"type": "string", "description": "A list of existing services to generate clients for. Select 'all-service' to generate all clients.", "enum": ["all-service", "application", "authorization-server", "billing", "business-case-manager", "communication", "company", "contract-management", "dashboard", "demo", "doc-info-filler", "document", "document-forwarding", "document-generator", "document-preview", "document-signing", "exchange", "external-integrations", "feedback", "financing-details", "handelsregister", "internal-tools", "neo-gpt", "notification", "platform-notification", "portal", "watchlist"], "x-priority": "important", "alias": "s"}, "newServiceName": {"type": "string", "description": "If the name of the service is missing in the list of existing service write it down here.", "x-priority": "important", "alias": "n"}}, "required": ["filePath"], "additionalProperties": false}