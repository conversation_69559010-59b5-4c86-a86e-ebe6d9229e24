{"$schema": "http://json-schema.org/draft-07/schema", "cli": "nx", "id": "swagger-generator", "type": "object", "title": "Swagger Generator", "description": "Generate Angular services and types from Swagger/OpenAPI JSON specifications for microservices", "properties": {"filePath": {"type": "string", "description": "Relative path to the Swagger/OpenAPI JSON file within the workspace. The generator will read this file to extract API definitions and generate corresponding Angular services and TypeScript types. Example: 'api-docs.json' or 'swagger/my-service.json'", "default": "api-docs.json", "x-prompt": "What is the path to the Swagger JSON file?", "alias": "f"}, "branchName": {"type": "string", "description": "Git branch name from which to download the Swagger JSON file via GitLab artifacts. This is used when the Swagger file needs to be fetched from a remote repository rather than using a local file. Leave empty to use the local file specified in filePath.", "alias": "b", "x-priority": "important"}, "projectName": {"type": "string", "description": "Name of the Nx project where the generated Angular services and types will be created. This should be an existing library project in your workspace that will serve as the container for the generated API clients. The generator will create secondary entry points within this project for each microservice.", "default": "swagger-generator", "x-prompt": "Which project should be used for the swagger generator?", "alias": "p"}, "requiredDemoController": {"type": "array", "description": "List of microservice names that must include a 'single-cluster-demo-controller' endpoint in their Swagger specification. The generator will validate that these services contain the required demo controller and throw an error if it's missing. This ensures consistency across demo environments for specified microservices.", "default": ["authorization-server", "business-case-manager", "company", "financing-details", "portal"], "items": {"type": "string", "description": "Microservice name that requires demo controller validation"}, "alias": "d"}, "selectService": {"type": "string", "description": "Select a specific microservice to generate API clients for, or choose 'all-service' to generate clients for all available services. This option allows you to target specific services when you don't want to regenerate all API clients. Note: Currently not implemented in the generator logic - the service name is automatically extracted from the Swagger specification.", "enum": ["all-service", "application", "authorization-server", "billing", "business-case-manager", "communication", "company", "contract-management", "dashboard", "demo", "doc-info-filler", "document", "document-forwarding", "document-generator", "document-preview", "document-signing", "exchange", "external-integrations", "feedback", "financing-details", "handelsregister", "internal-tools", "neo-gpt", "notification", "platform-notification", "portal", "watchlist"], "x-priority": "important", "alias": "s"}, "newServiceName": {"type": "string", "description": "Specify a custom microservice name if it's not available in the predefined list of services. This allows you to generate API clients for new or unlisted microservices. The name should match the microservice identifier used in your system. Note: Currently not implemented in the generator logic - the service name is automatically extracted from the Swagger specification.", "x-priority": "important", "alias": "n"}}, "required": ["filePath"], "additionalProperties": false}