import { Gitlab } from '@gitbeaker/rest';
import * as AdmZip from 'adm-zip';
import { printChanges } from 'nx/src/generators/tree';
import * as path from 'path';

// Configuration
const GITLAB_HOST = 'https://git.neo.loan'; // Change to your GitLab host if self-managed
const GITLAB_TOKEN = '**************************';
const PROJECT_IDENTIFIER = 'fincloud/srv-business-case-manager'; // e.g., 'namespace/project' or numeric ID
const TARGET_BRANCH = 'development';
const TARGET_JOB_NAME = 'maven_generate_swagger';

// GitLab client instance
const gitlabApi = new Gitlab({
  host: GITLAB_HOST,
  token: GITLAB_TOKEN,
});

/**
 * Get the latest pipeline ID for a specific branch
 */
async function getLatestPipelineId(
  projectIdentifier: string,
  branchName: string
) {
  const pipelines = await gitlabApi.Pipelines.showLatest(projectIdentifier, {
    ref: branchName,
  });

  if (pipelines.length === 0) {
    throw new Error(`No pipelines found for branch: ${branchName}`);
  }

  const latestPipeline = pipelines;
  return latestPipeline.id;
}

/**
 * Find a job by name in the pipeline that has artifacts
 */
async function getJobWithArtifactsByName(
  projectIdentifier: string,
  pipelineId: number,
  jobName: string
) {
  const pipelineJobs = await gitlabApi.Jobs.all(projectIdentifier, {
    pipelineId,
  });

  for (const job of pipelineJobs) {
    const hasArtifacts = !!(job.artifacts_file && job.artifacts_file.filename);
    const nameMatches = job.name === jobName;

    if (hasArtifacts === true && nameMatches === true) {
      return job;
    }
  }

  throw new Error(
    `Job named "${jobName}" with artifacts not found in pipeline ${pipelineId}`
  );
}

/**
 * Download artifacts for a specific job
 */
async function downloadArtifact(projectIdentifier: string, jobId: number) {
  const artifactBuffer = await gitlabApi.JobArtifacts.downloadArchive(
    projectIdentifier,
    { jobId }
  );
  return Buffer.from(await artifactBuffer.arrayBuffer());
}

// Main execution block
export const collectArtifacts = async (projectRoot = '') => {
  try {
    const pipelineId = await getLatestPipelineId(
      PROJECT_IDENTIFIER,
      TARGET_BRANCH
    );
    const job = await getJobWithArtifactsByName(
      PROJECT_IDENTIFIER,
      pipelineId,
      TARGET_JOB_NAME
    );

    const artifact = await downloadArtifact(PROJECT_IDENTIFIER, job.id);
    extractArtifacts(artifact);

    printChanges([
      {
        type: 'CREATE',
        content: artifact,
        path: projectRoot + 'swagger.json',
      },
    ]);
  } catch (error) {
    throw new Error(error.message);
  }
};

const extractArtifacts = async (artifact: Buffer) => {
  const zip = new AdmZip(artifact);
  zip.extractAllTo('./', true);
};
