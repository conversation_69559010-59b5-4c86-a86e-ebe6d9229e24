{"compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "commonjs", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@fincloud/nx-plugin": ["src/index.ts"]}}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}