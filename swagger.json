{"openapi": "3.0.1", "info": {"title": "Srv-business-case-manager API", "version": "5.0.0"}, "servers": [{"url": ""}, {"url": "/api/srv-business-case-manager"}], "security": [{"bearerAuth": []}], "paths": {"/{businessCaseId}/faq/{faqId}": {"put": {"tags": ["faq-controller"], "operationId": "updateFaq", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "faqId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "sendNotification", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFaqRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}}, "delete": {"tags": ["faq-controller"], "operationId": "deleteFaq", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "faqId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/template/{templateId}": {"get": {"tags": ["template-controller"], "operationId": "getTemplateByUuid", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}}, "put": {"tags": ["template-controller"], "operationId": "updateTemplate", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateUpdateResponse"}}}}}}}, "/template/{templateId}/account-manager/edit-case-related-template/{customerKey}": {"put": {"tags": ["template-controller"], "operationId": "editTemplateByAccountManager", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateUpdateResponse"}}}}}}}, "/migration/template/add-folder-structure/template": {"put": {"tags": ["migration-folder-structure-in-template-controller"], "operationId": "migrateFolderStructureInTemplate", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/migration/template/add-folder-structure/replace-special-characters-informations-and-revisions": {"put": {"tags": ["migration-folder-structure-in-template-controller"], "operationId": "replaceSpecialCharactersInInformationsAndRevisions", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/migration/template/add-folder-structure/business-case-template": {"put": {"tags": ["migration-folder-structure-in-template-controller"], "operationId": "migrateFolderStructureInBusinessCase", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/internal/facility-management/catalog/{facilityId}": {"put": {"tags": ["facilities-internal-controller"], "operationId": "replaceCatalogFacilityInternal", "parameters": [{"name": "facilityId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FacilityDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Facility"}}}}}}, "delete": {"tags": ["facilities-internal-controller"], "operationId": "deleteCatalogFacilityInternal", "parameters": [{"name": "facilityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/information": {"put": {"tags": ["information-controller"], "operationId": "updateInformationValue", "parameters": [{"name": "informationId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}, "post": {"tags": ["information-controller"], "operationId": "getAllInformation", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "includeMasterBusinessCaseInfo", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "includeDeleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldKeysForInclusionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}}, "/information/portal-edit": {"put": {"tags": ["information-controller"], "operationId": "updateInformationValueFromPortal", "parameters": [{"name": "informationId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}}, "/facility-management/{businessCaseId}/update-facility/{name}": {"put": {"tags": ["facilities-controller"], "operationId": "updateConfigFacility", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityField"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/facility-management/{businessCaseId}/enable-or-disable-facility": {"put": {"tags": ["facilities-controller"], "operationId": "enableOrDisableBusinessCaseFacility", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "facilityName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "isEnabled", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/facility-management/{businessCaseId}/add-facility/{name}": {"put": {"tags": ["facilities-controller"], "operationId": "addFacilityToBusinessCaseConfig", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/facility-management/{businessCaseId}/add-facility-field-value/{name}": {"put": {"tags": ["facilities-controller"], "operationId": "addConfiguraitonFacilityFieldValue", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "value", "in": "query", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/{fieldKey}/add-value": {"put": {"tags": ["business-case-controller"], "operationId": "addFieldValueForBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/update-template-name": {"put": {"tags": ["business-case-controller"], "operationId": "updateBusinessCaseTemplateName", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "templateName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/update-participation-amount": {"put": {"tags": ["participant-controller"], "operationId": "updateParticipationAmount", "parameters": [{"name": "totalParticipationAmount", "in": "query", "required": true, "schema": {"type": "number", "format": "double"}}, {"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCustomer"}}}}}}}, "/business-case/{businessCaseId}/update-participant-state-perception": {"put": {"tags": ["participant-controller"], "operationId": "updateParticipantStatePerception", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "newState", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/business-case/{businessCaseId}/update-min-max-participation-amount": {"put": {"tags": ["business-case-controller"], "operationId": "updateMinMaxParticipationAmount", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "minParticipationAmount", "in": "query", "required": false, "schema": {"type": "number", "format": "double"}}, {"name": "maxParticipationAmount", "in": "query", "required": false, "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK"}}}}, "/business-case/{businessCaseId}/update-customer-participation-amount": {"put": {"tags": ["participant-controller"], "operationId": "updateCustomerParticipationAmount", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "totalParticipationAmount", "in": "query", "required": true, "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCustomer"}}}}}}}, "/business-case/{businessCaseId}/participant-customer/participant-user/{userId}": {"put": {"tags": ["participant-controller"], "operationId": "addParticipantUserToBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCustomer"}}}}}}, "delete": {"tags": ["participant-controller"], "operationId": "removeUserFromBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/business-case/{businessCaseId}/participant-customer/participant-user/current-users": {"put": {"tags": ["participant-controller"], "operationId": "getCurrentUsersForCustomerInBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrentUsersInBusinessCaseResponse"}}}}}}}, "/business-case/{businessCaseId}/link-cadr": {"put": {"tags": ["business-case-controller"], "operationId": "linkCADR", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "linked", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/guest-customer/{customerKey}/guest-users": {"put": {"tags": ["participant-controller"], "operationId": "addParticipantGuestUsersToBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserData"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCustomer"}}}}}}}, "/business-case/{businessCaseId}/folder": {"put": {"tags": ["folder-controller"], "operationId": "updateFolder", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFolderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}, "post": {"tags": ["folder-controller"], "operationId": "createFolder", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFolderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}, "delete": {"tags": ["folder-controller"], "operationId": "deleteFolder", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFolderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/folder/move-folder": {"put": {"tags": ["folder-controller"], "operationId": "moveFolder", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MoveFolderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/folder/move-field": {"put": {"tags": ["folder-controller"], "operationId": "moveFieldBetweenFolders", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MoveFieldBetweenFoldersRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/edit-groups": {"put": {"tags": ["business-case-controller"], "operationId": "editBusinessCaseGroups", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/edit-field/{fieldKey}": {"put": {"tags": ["business-case-controller"], "operationId": "editBusinessCaseField", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldEditRequestBody"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDto"}}}}}}}, "/business-case/{businessCaseId}/add-field": {"put": {"tags": ["business-case-controller"], "operationId": "addFieldToBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case-permissions/add-permission-to-participant": {"put": {"tags": ["case-permission-controller"], "operationId": "addPermissionToParticipant", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "permissionSetId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "permissionCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCasePermissionSetEntity"}}}}}}}, "/{businessCaseId}/faq": {"get": {"tags": ["faq-controller"], "operationId": "getFaqs", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Faq"}}}}}}}, "post": {"tags": ["faq-controller"], "operationId": "createFaq", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "sendNotification", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFaqRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Faq"}}}}}}}, "/template": {"get": {"tags": ["template-controller"], "operationId": "getAllTemplates", "parameters": [{"name": "showExcluded", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "include<PERSON>ieldsWith<PERSON>ey", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}}}}}}, "post": {"tags": ["template-controller"], "operationId": "createTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}}}, "/template/{templateId}/add-custom-fields": {"post": {"tags": ["template-controller"], "operationId": "addCustomField", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}}}, "/template/{templateId}/add-catalogue-fields": {"post": {"tags": ["template-controller"], "operationId": "addCatalogueFields", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}}}, "/template/account-manager/create-case-related-template/{customerKey}": {"post": {"tags": ["template-controller"], "operationId": "createTemplateByAccountManager", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}}}, "/migration/business-case/neo-gpt-information-update/informations": {"post": {"tags": ["migration-neo-gpt-information-update-controller"], "operationId": "sendUpdateToNeoGptForActiveInformations", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}}, "/migration/business-case/delete-passing-cases": {"post": {"tags": ["migration-delete-cases-controller"], "operationId": "deleteAllCasesAndParticipantsPermissions", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/manager/business-case/{businessCaseId}/add-me-to-case": {"post": {"tags": ["manager-controller"], "operationId": "addParticipantUserToBusinessCase_1", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCustomer"}}}}}}}, "/leadership/{businessCaseId}/request-transfer-leadership/target-customer/{newLeaderCustomerKey}": {"post": {"tags": ["transfer-leadership-controller"], "operationId": "requestTransferLeadership", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "newLeaderCustomerKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "shouldShareCADRExplicitlyWithNewLeader", "in": "query", "required": true, "schema": {"type": "boolean"}}, {"name": "shouldShareCADRExplicitlyWithAllParticipants", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/leadership/{businessCaseId}/reject-transfer-leadership/target-customer/{newLeaderCustomerKey}": {"post": {"tags": ["transfer-leadership-controller"], "operationId": "rejectTransferLeadership", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "newLeaderCustomerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/leadership/{businessCaseId}/accept-transfer-leadership/target-customer/{newLeaderCustomerKey}": {"post": {"tags": ["transfer-leadership-controller"], "operationId": "acceptTransferLeadership", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "newLeaderCustomerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferLeadershipTransferRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/internal/test-engine": {"post": {"tags": ["engine-test-controller"], "operationId": "testEngineInternal", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EvaluationResult"}}}}}}}, "/internal/interested-customers/remove-invitees/{businessCaseId}": {"post": {"tags": ["interested-customers-internal-controller"], "operationId": "removeInviteesFromInterestedCustomerInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "invitees", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/internal/interested-customers/remove-applicants/{businessCaseId}": {"post": {"tags": ["interested-customers-internal-controller"], "operationId": "removeApplicantsFromInterestedCustomerInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "applicants", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/internal/interested-customers/clear/{businessCaseId}": {"post": {"tags": ["interested-customers-internal-controller"], "operationId": "removeAllInterestedCustomerInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/internal/interested-customers/add-invitees/{businessCaseId}": {"post": {"tags": ["interested-customers-internal-controller"], "operationId": "addInviteesToInterestedCustomerInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomerAddRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/internal/interested-customers/add-applicants/{businessCaseId}": {"post": {"tags": ["interested-customers-internal-controller"], "operationId": "addApplicantsToInterestedCustomerInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomerAddRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/internal/information": {"post": {"tags": ["information-internal-controller"], "operationId": "getAllInformationInternal", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "includeMasterBusinessCaseInfo", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "includeDeleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldKeysForInclusionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}}, "/internal/information/by-fields": {"post": {"tags": ["information-internal-controller"], "operationId": "getInformationIdsByFieldsInternal", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InformationIdsByFieldsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/internal/information/by-business-case": {"post": {"tags": ["information-internal-controller"], "operationId": "getFilteredInformationByBusinessCaseForCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerBusinessCaseInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}}, "/internal/field-history/business-cases": {"post": {"tags": ["field-history-controller"], "operationId": "getAllFieldHistoriesGroupedByBusinessCaseIdAscOrder", "parameters": [{"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "fieldType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["TOTAL_PARTICIPATION_AMOUNT", "MARKET_VALUE"]}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/FieldHistory"}}}}}}}}}, "/internal/field-history/business-case/{businessCaseId}/add-field-history": {"post": {"tags": ["field-history-controller"], "operationId": "addFieldHistory", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFieldHistoryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/internal/context-case-information/reload-context": {"post": {"tags": ["case-context-information-internal-controller"], "operationId": "reloadCaseContext", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/internal/context-case-information/participant-case-permission-sets": {"post": {"tags": ["case-context-information-internal-controller"], "operationId": "getParticipantCasePermissionSetsForBusinessCases", "requestBody": {"content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ParticipantCasePermissionSetEntity"}}}}}}}}, "/internal/business-case/{businessCaseId}/{customerKey}": {"post": {"tags": ["business-case-internal-controller"], "operationId": "getBusinessCaseByIdInternalWithSetupAuthentication", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldKeysForInclusionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/internal/business-case/{businessCaseId}/participant-customer/{customerKey}": {"post": {"tags": ["participant-internal-controller"], "operationId": "addCustomerToBusinessCaseInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "tokenCallerUserId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertCustomerToBusinessCaseDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCustomer"}}}}}}}, "/internal/business-case/{businessCaseId}/create-subcase": {"post": {"tags": ["business-case-internal-controller"], "operationId": "createSubCaseInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubCaseCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/internal/business-case/{businessCaseId}/change-participation-type": {"post": {"tags": ["business-case-internal-controller"], "operationId": "changeParticipationType", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "participationType", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/internal/business-case/portal/{businessCaseId}": {"post": {"tags": ["business-case-internal-controller"], "operationId": "getBusinessCaseByIdPortalInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldKeysForInclusionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/internal/business-case/participation/{userId}": {"post": {"tags": ["business-case-internal-controller"], "operationId": "getUserParticipationInBusinessCasesInternal", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "states", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["ACTIVE_PRIVATE", "ACTIVE_PUBLIC", "INACTIVE_CANCELLED", "INACTIVE_COMPLETED"]}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserParticipationInBusinessCaseResponse"}}}}}}}, "/internal/business-case/modify-case-permissions": {"post": {"tags": ["business-case-internal-controller"], "operationId": "modifyCasePermissionSetsForCustomers", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateCasePermissionsRequest"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/internal/business-case/modify-case-permissions-refactored": {"post": {"tags": ["business-case-internal-controller"], "operationId": "modifyCasePermissionSetsForCustomers2", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/internal/business-case/information-revisions": {"post": {"tags": ["business-case-internal-controller"], "operationId": "deleteRevisionsForUsers", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/interested-customers/remove-communicators/{businessCaseId}": {"post": {"tags": ["interested-customers-controller"], "operationId": "removeCommunicatorsFromInterestedCustomer", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "communicators", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/interested-customers/add-communicators/{businessCaseId}": {"post": {"tags": ["interested-customers-controller"], "operationId": "addCommunicatorsToInterestedCustomer", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "communicators", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/information/restore": {"post": {"tags": ["information-controller"], "operationId": "restoreRevision", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "revisionId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/facility-management/{businessCaseId}/restore-revision/{revisionId}": {"post": {"tags": ["facilities-controller"], "operationId": "restoreRevision_1", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "revisionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/duplicate/{businessCaseId}": {"post": {"tags": ["duplicate-business-case-controller"], "operationId": "duplicateBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DuplicateBusinessCaseRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DuplicateBusinessCaseResponse"}}}}}}}, "/demo/recall": {"post": {"tags": ["single-cluster-demo-controller"], "operationId": "recall", "parameters": [{"name": "uniqueIdentifier", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "callerUserId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/demo/deploy": {"post": {"tags": ["single-cluster-demo-controller"], "operationId": "deploy", "parameters": [{"name": "uniqueIdentifier", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "callerUserId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeployBusinessCaseRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "string"}}}}}}}, "/contact-person/{businessCaseId}": {"post": {"tags": ["contact-person-controller"], "operationId": "addContactPersons", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}, "deprecated": true}, "delete": {"tags": ["contact-person-controller"], "operationId": "deleteAllContactPersons", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "deprecated": true}}, "/business-case": {"post": {"tags": ["business-case-controller"], "operationId": "createBusinessCase", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBusinessCaseRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCaseResponse"}}}}}}}, "/business-case/{businessCaseId}": {"post": {"tags": ["business-case-controller"], "operationId": "getBusinessCaseById", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldKeysForInclusionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/state/transfer/{stateTransfer}": {"post": {"tags": ["business-case-controller"], "operationId": "setState", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "stateTransfer", "in": "path", "required": true, "schema": {"type": "string", "enum": ["HIDE", "UNHIDE", "PUBLISH", "UNPUBLISH", "COMPLETE", "REACTIVATE", "CANCEL"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/re-synchronize": {"post": {"tags": ["business-case-controller"], "operationId": "reSynchronizeSubcase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/business-case/{businessCaseId}/re-synchronize-information-entry/{informationKey}": {"post": {"tags": ["business-case-controller"], "operationId": "reSynchronizeInformationEntry", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "informationKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Information"}}}}}}}, "/business-case/{businessCaseId}/folder/compare-structure": {"post": {"tags": ["folder-controller"], "operationId": "compareFolderStructureForBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FolderStructureComparisonRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/business-case/{businessCaseId}/folder/add-field": {"post": {"tags": ["folder-controller"], "operationId": "addFieldToFolder", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFieldToFolderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/participation/{userId}": {"post": {"tags": ["business-case-controller"], "operationId": "getUserParticipationInBusinessCases", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "states", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["ACTIVE_PRIVATE", "ACTIVE_PUBLIC", "INACTIVE_CANCELLED", "INACTIVE_COMPLETED"]}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserParticipationInBusinessCaseResponse"}}}}}}}, "/business-case/hard-delete-field/{businessCaseId}": {"post": {"tags": ["business-case-controller"], "operationId": "deleteFieldByKey", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/edit-and-merge-groups": {"patch": {"tags": ["business-case-controller"], "operationId": "editAndMergeBusinessCaseGroups", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/template/{templateId}/version-history-by-id": {"get": {"tags": ["template-controller"], "operationId": "getVersionHistoryOfTemplateByUuid", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateVersionHistoryResponse"}}}}}}}, "/template/version-history-by-name": {"get": {"tags": ["template-controller"], "operationId": "getVersionHistoryOfTemplateByName", "parameters": [{"name": "templateName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateVersionHistoryResponse"}}}}}}}, "/template/account-manager/get-all-case-related-template/{customerKey}": {"get": {"tags": ["template-controller"], "operationId": "getAllTemplatesByAccountManager", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}}}}}}}, "/migration/template/add-folder-structure/complex-fields": {"get": {"tags": ["migration-folder-structure-in-template-controller"], "operationId": "checkComplexFields", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/migration/template/add-folder-structure/case-template-fetch-fields-without-groups": {"get": {"tags": ["migration-folder-structure-in-template-controller"], "operationId": "fetchFieldsWithoutGroupsForCaseTemplate", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/migration/template/add-folder-structure/business-case-fetch-fields-without-groups": {"get": {"tags": ["migration-folder-structure-in-template-controller"], "operationId": "fetchFieldsWithoutGroupsForBusinessCase", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/internal/interested-customers/{businessCaseId}": {"get": {"tags": ["interested-customers-internal-controller"], "operationId": "getInterestedCustomersForBusinessCaseInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/internal/interested-customers/interested-cleanup": {"get": {"tags": ["interested-customers-internal-controller"], "operationId": "cleanupInterestedCustomers", "responses": {"200": {"description": "OK"}}}}, "/internal/interested-customers/business-cases-for-company/{companyId}": {"get": {"tags": ["interested-customers-internal-controller"], "operationId": "getInterestedCustomersForBusinessCasesPerCompanyInternal", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}}, "/internal/information/{informationKey}": {"get": {"tags": ["information-internal-controller"], "operationId": "getInformationInternal", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "informationKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Information"}}}}}}}, "/internal/information/id/{informationId}": {"get": {"tags": ["information-internal-controller"], "operationId": "getInformationByInformationId", "parameters": [{"name": "informationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Information"}}}}}}}, "/internal/field-history/business-cases/{businessCaseId}": {"get": {"tags": ["field-history-controller"], "operationId": "getAllFieldsByBusinessCaseIdAndCustomerKeyAndHistoryFieldTypeAscOrder", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "fieldType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["TOTAL_PARTICIPATION_AMOUNT", "MARKET_VALUE"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FieldHistory"}}}}}}}}, "/internal/export-controller/get-case-with-mapped-template/{businessCaseId}": {"get": {"tags": ["export-controller"], "operationId": "getMappedBusinessCaseInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/internal/context-case-information/{businessCaseId}": {"get": {"tags": ["case-context-information-internal-controller"], "operationId": "getContextCreationInformationInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReloadContextRequest"}}}}}}}, "/internal/context-case-information/{businessCaseId}/get-participant-permission-set": {"get": {"tags": ["case-context-information-internal-controller"], "operationId": "getParticipantPermissionSetInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCasePermissionSetEntity"}}}}}}}, "/internal/context-case-information/{businessCaseId}/get-default-case-permissions": {"get": {"tags": ["case-context-information-internal-controller"], "operationId": "getDefaultCasePermissionsInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "appliedOnState", "in": "query", "required": false, "schema": {"type": "string", "enum": ["INTERESTED", "COLLABORATOR", "PARTICIPANT", "LEADER"]}}, {"name": "participationType", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/DefaultParticipantCasePermissionSet"}}}}}}}}, "/internal/context-case-information/participant-visibility/{businessCaseId}": {"get": {"tags": ["case-context-information-internal-controller"], "operationId": "getParticipantVisibilityInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantVisibilityDTO"}}}}}}}, "/internal/business-case": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getAllBusinessCasesInternal", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}}, "/internal/business-case/{companyId}/other-business-cases": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getOtherBusinessCasesForCompanyInternal", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/internal/business-case/{businessCaseId}": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getBusinessCaseByIdInternal", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/internal/business-case/total-investment-amount/{businessCaseId}": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getTotalInvestmentAmountByBusinessCaseId", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "number", "format": "double"}}}}}}}, "/internal/business-case/paged": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getPagedBusinessCases", "parameters": [{"name": "page", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBusinessCase"}}}}}}}, "/internal/business-case/paged/last-modified": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getLastModifiedPagedBusinessCases", "parameters": [{"name": "page", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lastModified", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageBusinessCase"}}}}}}}, "/internal/business-case/lead-customer-key/{businessCaseId}": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getBusinessCaseLeadCustomerKey", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/internal/business-case/check-participation-per-company": {"get": {"tags": ["business-case-internal-controller"], "operationId": "checkParticipationPerCompanyInternal", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/internal/business-case/business-case-ids": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getAllBusinessCaseIdsInternal", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/internal/business-case/all/{companyId}": {"get": {"tags": ["business-case-internal-controller"], "operationId": "getBusinessCasesByCompanyIdInternalInternal", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/interested-customers/{businessCaseId}": {"get": {"tags": ["interested-customers-controller"], "operationId": "getInterestedCustomersForBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestedCustomers"}}}}}}}, "/information/{informationKey}": {"get": {"tags": ["information-controller"], "operationId": "getInformation", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "informationKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Information"}}}}}}}, "/information/unique-information-keys-for-customer": {"get": {"tags": ["information-controller"], "operationId": "getUniqueInformationKeysForCustomerWithoutDocuments", "parameters": [{"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/information/revisions": {"get": {"tags": ["information-controller"], "operationId": "getAllRevisions", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "informationIdentifierKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "showDeleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InformationRevision"}}}}}}}}, "/information/revision/{id}": {"get": {"tags": ["information-controller"], "operationId": "getRevision", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InformationRevision"}}}}}}}, "/information/global-required-fields": {"get": {"tags": ["information-controller"], "operationId": "getGlobalRequiredFields", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/information/by-group": {"get": {"tags": ["information-controller"], "operationId": "getByGroup", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "group", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "includeDeleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "includeWithoutGroup", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}, {"name": "fieldKeysToBeIncluded", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}}}, "/facility-management/{businessCaseId}/revision/all": {"get": {"tags": ["facilities-controller"], "operationId": "getAllRevisionForCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "facilityName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "facilityFieldKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityRevision"}}}}}}}}, "/facility-management/{businessCaseId}/facilities": {"get": {"tags": ["facilities-controller"], "operationId": "getBusinessCaseFacilities", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/Facility"}}}}}}}}, "/facility-management/catalog": {"get": {"tags": ["facilities-controller"], "operationId": "getAllCatalogFacilities", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Facility"}}}}}}}}, "/demo": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getBusinessCaseData", "parameters": [{"name": "businessCaseId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/BusinessCaseData"}}}}}}}, "/demo/all-by-unique-identifier/{uniqueIdentifier}": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getAllBusinessCaseIdsByUniqueIdentifier", "parameters": [{"name": "uniqueIdentifier", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/case-context/{businessCaseId}": {"get": {"tags": ["case-context-information-controller"], "operationId": "getContextInformation", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCasePermissionSetEntity"}}}}}}}, "/case-context/participant-visibility/{businessCaseId}": {"get": {"tags": ["case-context-information-controller"], "operationId": "getParticipantVisibility", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantVisibilityDTO"}}}}}}}, "/business-case/{companyId}/other-business-cases": {"get": {"tags": ["business-case-controller"], "operationId": "getOtherBusinessCasesForCompany", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/business-case/{businessCaseId}/get-sub-case": {"get": {"tags": ["business-case-controller"], "operationId": "getSubCaseForMasterBusinessCaseId", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/business-case/{businessCaseId}/get-state-info": {"get": {"tags": ["business-case-controller"], "operationId": "getStateInformation", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCaseStateInformation"}}}}}}}, "/business-case/{businessCaseId}/folder/{folderId}": {"get": {"tags": ["folder-controller"], "operationId": "getFolder", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "folderId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Folder"}}}}}}}, "/business-case/participant-structurer-case-state-perception": {"get": {"tags": ["business-case-controller"], "operationId": "participantStructurerCaseStatePerception", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "enum": ["STRUCTURING_ORDER_ISSUED", "TERM_SHEET_ACCEPTED", "FINANCING_APPROVED", "FULL_EVALUATION_CARRIED_OUT", "FINANCING_REPAID"]}}}}}}}}, "/business-case/participant-participant-case-state-perception": {"get": {"tags": ["business-case-controller"], "operationId": "participantParticipantCaseStatePerception", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "enum": ["FINANCING_CASE_JOINED", "FINANCING_APPROVED", "FULL_EVALUATION_CARRIED_OUT", "FINANCING_REPAID"]}}}}}}}}, "/business-case/leader-leader-closed-case-state-perception": {"get": {"tags": ["business-case-controller"], "operationId": "getClosedLeaderLeaderCaseStatePerceptions", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "enum": ["FINANCING_CASE_CREATED", "FINANCING_APPLICATION_SUBMITTED", "STRUCTURING_ORDER_ISSUED", "TERM_SHEET_ACCEPTED", "FINANCING_APPROVED", "FULL_EVALUATION_CARRIED_OUT", "FINANCING_REPAID", "OBJECT_SOLD", "FINANCING_REJECTED", "FINANCING_APPLICATION_WITHDRAWN", "OTHER", "INCORRECT_ENTRY"]}}}}}}}}, "/business-case/leader-leader-case-state-perception": {"get": {"tags": ["business-case-controller"], "operationId": "getLeaderLeaderCaseStatePerceptions", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "enum": ["FINANCING_CASE_CREATED", "FINANCING_APPLICATION_SUBMITTED", "STRUCTURING_ORDER_ISSUED", "TERM_SHEET_ACCEPTED", "FINANCING_APPROVED", "FULL_EVALUATION_CARRIED_OUT", "FINANCING_REPAID", "OBJECT_SOLD", "FINANCING_REJECTED", "FINANCING_APPLICATION_WITHDRAWN", "OTHER", "INCORRECT_ENTRY"]}}}}}}}}, "/business-case/get-master-case-leader/{masterBusinessCaseId}": {"get": {"tags": ["business-case-controller"], "operationId": "getMasterCaseLeader", "parameters": [{"name": "masterBusinessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrentLeadResponse"}}}}}}}, "/business-case/check-participation-per-company": {"get": {"tags": ["business-case-controller"], "operationId": "checkParticipationPerCompany", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/business-case/all/{companyId}": {"get": {"tags": ["business-case-controller"], "operationId": "getBusinessCasesByCompanyId", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/business-case/all-participation": {"get": {"tags": ["business-case-controller"], "operationId": "getMyBusinessCases", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/business-case-permissions/get-participant-permission": {"get": {"tags": ["case-permission-controller"], "operationId": "getParticipantPermission", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCasePermissionSetEntity"}}}}}}}, "/internal/physical-delete/{customerKey}/physically-delete-person": {"delete": {"tags": ["physical-deletion-controller"], "operationId": "physicallyDeleteCustomerUserFromAllCasesInternal", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userIdToBeDeleted", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "managerUserId", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/facility-management/{businessCaseId}/remove-facility/{name}": {"delete": {"tags": ["facilities-controller"], "operationId": "removeFacilityFromBusinessCaseConfig", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCase"}}}}}}}, "/contact-person/{businessCaseId}/{userId}": {"delete": {"tags": ["contact-person-controller"], "operationId": "deleteSingleContactPerson", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}, "deprecated": true}}, "/business-case/{businessCaseId}/participant-customer/{customerKey}": {"delete": {"tags": ["participant-controller"], "operationId": "deleteCustomerFromBusinessCase", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/business-case/{businessCaseId}/delete-field/{fieldKey}": {"delete": {"tags": ["business-case-controller"], "operationId": "deleteField", "parameters": [{"name": "businessCaseId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/business-case-permissions/delete-permission-from-participant": {"delete": {"tags": ["case-permission-controller"], "operationId": "deletePermissionFromParticipant", "parameters": [{"name": "businessCaseId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "permissionSetId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "permissionCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParticipantCasePermissionSetEntity"}}}}}}}}, "components": {"schemas": {"UpdateFaqRequest": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}}}, "Faq": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "businessCaseId": {"type": "string"}, "question": {"type": "string"}, "answer": {"type": "string"}}}, "FieldDto": {"type": "object", "properties": {"key": {"type": "string"}, "fieldType": {"type": "string", "enum": ["SHORT_TEXT", "LONG_TEXT", "INTEGER", "DECIMAL", "MONETARY", "DATE", "DOCUMENT", "MONTHS", "PERCENT", "BOOLEAN", "LOCATION", "SELECT", "TABLE", "DATE_RANGE", "COMPOSITE", "MULTI_SELECT"]}, "isRequired": {"type": "boolean"}, "isPublic": {"type": "boolean"}, "isPromoted": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "label": {"type": "string"}, "visibilityExpression": {"type": "string"}, "expression": {"type": "string"}, "value": {"type": "object"}, "description": {"type": "string"}, "isHidden": {"type": "boolean"}, "fieldMetaData": {"type": "object"}, "fieldOwner": {"$ref": "#/components/schemas/FieldOwner"}, "categoryId": {"type": "string"}, "portalVisibility": {"type": "string", "enum": ["VISIBLE", "REQUESTED", "NOT_SET"]}, "dependantFields": {"type": "array", "items": {"type": "string"}}}}, "FieldOwner": {"type": "object", "properties": {"customerKey": {"type": "string"}}}, "Folder": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "parentId": {"type": "string"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Folder"}}, "fields": {"type": "array", "items": {"type": "string"}}, "ordinal": {"type": "integer", "format": "int64"}, "createdById": {"type": "string"}, "updatedById": {"type": "string"}}}, "GroupDto": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}, "fields": {"type": "array", "items": {"type": "string"}}, "rootFolder": {"$ref": "#/components/schemas/Folder"}, "portalVisibility": {"type": "string", "enum": ["VISIBLE", "REQUESTED", "NOT_SET"]}, "groupVisibility": {"$ref": "#/components/schemas/GroupVisibilityDto"}}}, "GroupVisibilityDto": {"type": "object", "properties": {"visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE", "PARTICIPANT", "INVITEE_AND_APPLICANT"]}, "visibleForNewParticipants": {"type": "boolean"}, "visibleForNewInterestedCustomers": {"type": "boolean"}}}, "TemplateDto": {"type": "object", "properties": {"name": {"type": "string"}, "defaultFinancingProduct": {"type": "string"}, "defaultIsPublic": {"type": "boolean"}, "defaultIsPromoted": {"type": "boolean"}, "versionDescription": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}, "groupsOrdered": {"type": "array", "items": {"$ref": "#/components/schemas/GroupDto"}}, "excludeFromSearch": {"type": "boolean"}, "financingStructureType": {"type": "string", "enum": ["REAL_ESTATE", "CORPORATE", "MISCELLANEOUS"]}}}, "EvaluationResult": {"type": "object", "properties": {"evaluatedFields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}, "hasError": {"type": "boolean"}, "failedReasons": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationFailureReason"}}}}, "Group": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}, "fields": {"type": "array", "items": {"type": "string"}}, "portalVisibility": {"type": "string", "enum": ["VISIBLE", "REQUESTED", "NOT_SET"]}, "groupVisibility": {"$ref": "#/components/schemas/GroupVisibility"}, "rootFolder": {"$ref": "#/components/schemas/Folder"}}}, "GroupVisibility": {"type": "object", "properties": {"visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE", "PARTICIPANT", "INVITEE_AND_APPLICANT"]}, "visibleForNewParticipants": {"type": "boolean"}, "visibleForNewInterestedCustomers": {"type": "boolean"}, "currentParticipantsWithVisibility": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "customerKeysInterestedToBeParticipants": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "Template": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "customerKey": {"type": "string"}, "defaultFinancingProduct": {"type": "string"}, "defaultIsPublic": {"type": "boolean"}, "defaultIsPromoted": {"type": "boolean"}, "version": {"type": "integer", "format": "int32"}, "versionDescription": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}, "isLatest": {"type": "boolean"}, "groupsOrdered": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}, "excludeFromSearch": {"type": "boolean"}, "financingStructureType": {"type": "string", "enum": ["REAL_ESTATE", "CORPORATE", "MISCELLANEOUS"]}}}, "TemplateUpdateResponse": {"type": "object", "properties": {"updatedTemplate": {"$ref": "#/components/schemas/Template"}, "businessCasesUpdated": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/EvaluationResult"}}}}, "ValidationFailureReason": {"type": "object", "properties": {"failedField": {"type": "string"}, "message": {"type": "string"}, "failedDependencies": {"type": "array", "items": {"type": "string"}}}}, "MigrationResult": {"type": "object", "properties": {"hasErrors": {"type": "boolean"}, "templateFieldsdErrorList": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateErrorResult"}}, "exceptionMessages": {"type": "array", "items": {"type": "string"}}}}, "TemplateErrorResult": {"type": "object", "properties": {"templateId": {"type": "string"}, "customerKey": {"type": "string"}, "templateName": {"type": "string"}, "fieldKey": {"type": "string"}, "groupKey": {"type": "string"}, "businessCaseId": {"type": "string"}, "errorMessage": {"type": "string"}, "autoGeneratedBusinessCaseName": {"type": "string"}}}, "FacilityDto": {"type": "object", "properties": {"name": {"type": "string"}, "ordinal": {"type": "integer", "format": "int32"}, "facilityFields": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityField"}}}}, "FacilityField": {"type": "object", "properties": {"key": {"type": "string"}, "label": {"type": "string"}, "fieldType": {"type": "string", "enum": ["SHORT_TEXT", "LONG_TEXT", "INTEGER", "DECIMAL", "MONETARY", "DATE", "DOCUMENT", "MONTHS", "PERCENT", "BOOLEAN", "LOCATION", "SELECT", "TABLE", "DATE_RANGE", "COMPOSITE", "MULTI_SELECT"]}, "value": {"type": "object"}, "fieldMetaData": {"type": "object"}, "isEditable": {"type": "boolean"}, "isUpdated": {"type": "boolean"}, "eligibleForCalculation": {"type": "boolean"}, "group": {"type": "string"}, "restoredFromRevision": {"type": "boolean"}, "restoredFromRevisionId": {"type": "string"}, "description": {"type": "string"}, "ordinal": {"type": "integer", "format": "int32"}}}, "Facility": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "ordinal": {"type": "integer", "format": "int32"}, "facilityFields": {"type": "array", "items": {"$ref": "#/components/schemas/FacilityField"}}, "enabledInCase": {"type": "boolean"}}}, "Information": {"required": ["businessCaseId", "key", "value"], "type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "key": {"type": "string"}, "businessCaseId": {"type": "string"}, "userId": {"type": "string"}, "value": {"type": "object"}, "isPublic": {"type": "boolean", "deprecated": true}, "isPromoted": {"type": "boolean"}, "field": {"$ref": "#/components/schemas/FieldDto"}, "revisionId": {"type": "string"}, "synced": {"type": "boolean"}, "informationState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}}}, "BillingStatus": {"type": "object", "properties": {"businessCaseId": {"type": "string"}, "status": {"type": "string", "enum": ["NOT_INVOICED", "INVOICE_REQUESTED", "REFUND_REQUESTED"]}, "loanAgreement": {"type": "string"}, "refundProof": {"type": "string"}}}, "BusinessCase": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "autoGeneratedBusinessCaseName": {"type": "string"}, "leadCustomerKey": {"type": "string"}, "chatId": {"type": "string"}, "companyId": {"type": "string"}, "businessCaseTemplate": {"$ref": "#/components/schemas/BusinessCaseTemplate"}, "participants": {"type": "array", "items": {"$ref": "#/components/schemas/ParticipantCustomer"}}, "state": {"type": "string", "enum": ["ACTIVE_PRIVATE", "ACTIVE_PUBLIC", "INACTIVE_CANCELLED", "INACTIVE_COMPLETED"]}, "masterBusinessCaseId": {"type": "string"}, "billingInformation": {"$ref": "#/components/schemas/BusinessCaseInvoiceGenerationResult"}, "billingInformationHistory": {"type": "array", "items": {"$ref": "#/components/schemas/BusinessCaseInvoiceGenerationResult"}}, "billingStatus": {"$ref": "#/components/schemas/BillingStatus"}, "minParticipationAmount": {"type": "number", "format": "double"}, "maxParticipationAmount": {"type": "number", "format": "double"}, "structuredFinancingConfiguration": {"$ref": "#/components/schemas/StructuredFinancingConfiguration"}, "contactPersonIds": {"type": "array", "items": {"type": "string"}}, "businessCaseType": {"type": "string", "enum": ["FINANCING_CASE", "PASSING_CASE"]}, "masterCaseInvitationApplicationAccepted": {"type": "boolean"}, "isCADRLinked": {"type": "boolean"}, "newLeadershipConfirmation": {"$ref": "#/components/schemas/NewLeadershipConfirmation"}, "isDuplicate": {"type": "boolean"}, "defaultPermissionSets": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/DefaultParticipantCasePermissionSet"}}}}, "BusinessCaseInvoiceGenerationResult": {"type": "object", "properties": {"businessCaseId": {"type": "string"}, "invoiceResults": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/InvoiceResult"}}, "term": {"type": "integer", "format": "int32"}, "hasFailedInvoices": {"type": "boolean"}}}, "BusinessCaseTemplate": {"type": "object", "properties": {"template": {"$ref": "#/components/schemas/Template"}, "isCustom": {"type": "boolean"}, "isLocked": {"type": "boolean"}, "masterTemplateId": {"type": "string"}}}, "DefaultParticipantCasePermissionSet": {"type": "object", "properties": {"name": {"type": "string"}, "appliedOnState": {"type": "string", "enum": ["INTERESTED", "COLLABORATOR", "PARTICIPANT", "LEADER"]}, "participationType": {"type": "string"}, "permissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "InvoiceResult": {"type": "object", "properties": {"hasError": {"type": "boolean"}, "errorMessage": {"type": "string"}, "invoice": {"type": "string"}}}, "NewLeadershipConfirmation": {"type": "object", "properties": {"newLeaderCustomerKey": {"type": "string"}, "currentConfirmationState": {"type": "string", "enum": ["REQUESTED", "ACCEPTED", "REJECTED", "COMPLETED"]}, "shouldShareCADRExplicitlyWithNewLeader": {"type": "boolean"}, "shouldShareCADRExplicitlyWithAllParticipants": {"type": "boolean"}}}, "ParticipantCustomer": {"type": "object", "properties": {"customerKey": {"type": "string"}, "participationStartOn": {"type": "string", "format": "date-time"}, "participationEndOn": {"type": "string", "format": "date-time"}, "users": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/ParticipantUser"}}, "totalParticipationAmount": {"type": "number", "format": "double"}, "participationType": {"type": "string"}, "participantPerceptionForCaseState": {"type": "string"}, "lead": {"type": "boolean"}}}, "ParticipantUser": {"type": "object", "properties": {"userId": {"type": "string"}, "joinedOn": {"type": "string", "format": "date-time"}, "customerKey": {"type": "string"}}}, "StructuredFinancingConfiguration": {"type": "object", "properties": {"isEditable": {"type": "boolean"}, "facilities": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/Facility"}}, "financingStructureType": {"type": "string", "enum": ["REAL_ESTATE", "CORPORATE", "MISCELLANEOUS"]}, "mainFinancingStructureId": {"type": "string", "deprecated": true}}}, "CurrentUsersInBusinessCaseResponse": {"type": "object", "properties": {"id": {"type": "string"}, "autoGeneratedBusinessCaseName": {"type": "string"}, "users": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/ParticipantUser"}}}}, "UserAttributes": {"type": "object", "properties": {"salutation": {"type": "string"}, "academicTitle": {"type": "string"}, "department": {"type": "string"}, "position": {"type": "string"}, "mobileNumber": {"type": "string"}, "landlineNumber": {"type": "string"}, "createdByUserId": {"type": "string"}, "acceptedTerms": {"type": "boolean"}, "companyUserId": {"type": "string"}, "companyName": {"type": "string"}, "locale": {"type": "string"}, "profilePictureId": {"type": "string"}, "lastUserSessionLocale": {"type": "string"}}}, "UserData": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "attributes": {"$ref": "#/components/schemas/UserAttributes"}}}, "UpdateFolderRequest": {"required": ["folderId", "folderName"], "type": "object", "properties": {"fieldKeysToBeIncluded": {"type": "array", "items": {"type": "string"}}, "folderId": {"type": "string"}, "folderName": {"type": "string"}}}, "MoveFolderRequest": {"required": ["folderGroup", "folderId", "targetFolderGroup", "targetFolderId"], "type": "object", "properties": {"fieldKeysToBeIncluded": {"type": "array", "items": {"type": "string"}}, "folderId": {"type": "string"}, "folderGroup": {"$ref": "#/components/schemas/Group"}, "targetFolderId": {"type": "string"}, "targetFolderGroup": {"$ref": "#/components/schemas/Group"}, "ordinal": {"type": "integer", "format": "int64"}}}, "MoveFieldBetweenFoldersRequest": {"required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "sourceFolderGroup", "targetFolderGroup", "targetFolderId"], "type": "object", "properties": {"fieldKeysToBeIncluded": {"type": "array", "items": {"type": "string"}}, "fieldKey": {"type": "string"}, "sourceFolderGroup": {"$ref": "#/components/schemas/Group"}, "targetFolderId": {"type": "string"}, "targetFolderGroup": {"$ref": "#/components/schemas/Group"}, "fieldLabel": {"type": "string"}}}, "FieldEditRequestBody": {"type": "object", "properties": {"label": {"type": "string"}, "description": {"type": "string"}, "fieldMetaData": {"type": "object"}, "isPublic": {"type": "boolean"}, "fieldType": {"type": "string", "enum": ["SHORT_TEXT", "LONG_TEXT", "INTEGER", "DECIMAL", "MONETARY", "DATE", "DOCUMENT", "MONTHS", "PERCENT", "BOOLEAN", "LOCATION", "SELECT", "TABLE", "DATE_RANGE", "COMPOSITE", "MULTI_SELECT"]}, "categoryId": {"type": "string"}}}, "ParticipantCasePermissionSetEntity": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "businessCaseId": {"type": "string"}, "customerKey": {"type": "string"}, "appliedOnState": {"type": "string", "enum": ["INTERESTED", "COLLABORATOR", "PARTICIPANT", "LEADER"]}, "participationType": {"type": "string"}, "permissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "CreateFaqRequest": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}}}, "TransferLeadershipTransferRequest": {"type": "object", "properties": {"adminUserIds": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "Param": {"type": "object", "properties": {"name": {"type": "string"}, "val": {"type": "object"}}}, "SimpleRequest": {"type": "object", "properties": {"fieldDtoList": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}, "paramList": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}}}, "InterestedCustomers": {"required": ["businessCaseId"], "type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "businessCaseId": {"type": "string"}, "invitees": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "applicants": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "communicators": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "InterestedCustomerAddRequest": {"type": "object", "properties": {"interestedCustomersForAddition": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/InterestedCustomerPayload"}}}}, "InterestedCustomerPayload": {"type": "object", "properties": {"customerKey": {"type": "string"}, "participationType": {"type": "string"}}}, "FieldKeysForInclusionRequest": {"type": "object", "properties": {"fieldKeysToBeIncluded": {"type": "array", "items": {"type": "string"}}}}, "InformationIdsByFieldsRequest": {"type": "object", "properties": {"fieldKeys": {"type": "array", "items": {"type": "string"}}}}, "CustomerBusinessCaseInfoRequest": {"type": "object", "properties": {"informationKey": {"type": "string"}, "businessCaseIds": {"type": "array", "items": {"type": "string"}}, "customerKey": {"type": "string"}}}, "FieldHistory": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "customerKey": {"type": "string"}, "businessCaseId": {"type": "string"}, "upsertDate": {"type": "string", "format": "date-time"}, "fieldValue": {"type": "string"}, "userId": {"type": "string"}, "fieldType": {"type": "string", "enum": ["TOTAL_PARTICIPATION_AMOUNT", "MARKET_VALUE"]}}}, "AddFieldHistoryRequest": {"type": "object", "properties": {"userId": {"type": "string"}, "fieldValue": {"type": "string"}, "historyFieldType": {"type": "string", "enum": ["TOTAL_PARTICIPATION_AMOUNT", "MARKET_VALUE"]}}}, "UpsertCustomerToBusinessCaseDto": {"type": "object", "properties": {"participantUsers": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/ParticipantUser"}}, "totalParticipationAmount": {"type": "number", "format": "double"}, "participationType": {"type": "string"}}}, "SubCaseCreateRequest": {"type": "object", "properties": {"userId": {"type": "string"}, "customerKey": {"type": "string"}, "participantUsers": {"type": "array", "items": {"$ref": "#/components/schemas/ParticipantUser"}}}}, "UserParticipationInBusinessCaseDTO": {"type": "object", "properties": {"businessCaseId": {"type": "string"}, "lastUserForParticipant": {"type": "boolean"}}}, "UserParticipationInBusinessCaseResponse": {"type": "object", "properties": {"userParticipation": {"type": "array", "items": {"$ref": "#/components/schemas/UserParticipationInBusinessCaseDTO"}}}}, "Address": {"type": "object", "properties": {"streetNameAndNumber": {"type": "string"}, "postalCode": {"type": "string"}, "cityName": {"type": "string"}}}, "BaseInfo": {"type": "object", "properties": {"bic": {"type": "string"}, "numEmployees": {"type": "integer", "format": "int32"}, "totalAssets": {"type": "number", "format": "double"}}}, "CasePermissionDTO": {"type": "object", "properties": {"code": {"type": "string"}, "description": {"type": "string"}, "appliedWith": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "removeIfThisApplied": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "removeWithThis": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "appliedOnlyForCustomerType": {"type": "array", "items": {"type": "string", "enum": ["BANK", "REAL_ESTATE", "CORPORATE", "INTERNAL", "FSP"]}}, "appliedOnlyForCustomerStatus": {"type": "array", "items": {"type": "string", "enum": ["REGULAR", "GUEST"]}}}}, "CasePermissionRequest": {"type": "object", "properties": {"permissionCode": {"type": "string"}, "casePermissionDTO": {"$ref": "#/components/schemas/CasePermissionDTO"}, "customerTypeAffectedSets": {"type": "string", "enum": ["ALL", "BANK_FSP_INTERNAL", "REAL_ESTATE_CORPO"]}, "affectedSetsBankFspInternal": {"type": "array", "items": {"type": "string"}}, "affectedSetsRealEstateCorpo": {"type": "array", "items": {"type": "string"}}, "shouldUpdateDefaultPermissionSets": {"type": "boolean"}, "shouldUpdateAllCustomerSets": {"type": "boolean"}, "shouldUpdateAllCaseSets": {"type": "boolean"}}}, "CollaborationSettings": {"type": "object", "properties": {"collaborationCustomers": {"type": "array", "items": {"type": "string"}}, "addNewCustomersToCollaborationList": {"type": "boolean"}}}, "Coordinates": {"type": "object", "properties": {"longitude": {"type": "string"}, "latitude": {"type": "string"}}}, "Customer": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "key": {"type": "string"}, "logo": {"type": "string"}, "email": {"$ref": "#/components/schemas/Email"}, "address": {"$ref": "#/components/schemas/Address"}, "coordinates": {"$ref": "#/components/schemas/Coordinates"}, "baseInfo": {"$ref": "#/components/schemas/BaseInfo"}, "website": {"type": "string"}, "collaborationSettings": {"$ref": "#/components/schemas/CollaborationSettings"}, "createdBy": {"type": "string"}, "customerOnboardingStatus": {"type": "string", "enum": ["NOT_ONBOARDED", "ONBOARDED"]}, "customerType": {"type": "string", "enum": ["BANK", "REAL_ESTATE", "CORPORATE", "INTERNAL", "FSP"]}, "customerStatus": {"type": "string", "enum": ["REGULAR", "GUEST"]}, "customerStateStatus": {"type": "string", "enum": ["CREATED", "ACTIVE", "DELETED"]}, "internal": {"type": "boolean"}, "mfaEnforced": {"type": "boolean"}, "themeName": {"type": "string"}, "salesChannel": {"type": "string", "enum": ["NEOSHARE", "BMS"]}, "bankingGroup": {"type": "string", "enum": ["VOLKSBANK", "SPARKASSE", "OTHER"]}, "allowedMfaTypes": {"type": "array", "items": {"type": "string", "enum": ["EMAIL", "SMS", "AUTHENTICATION_APP"]}}, "custom": {"type": "object"}}}, "Email": {"type": "object", "properties": {"address": {"type": "string"}}}, "UpdateCasePermissionsRequest": {"type": "object", "properties": {"operation": {"type": "string", "enum": ["ADD", "DELETE"]}, "code": {"type": "string"}, "customers": {"type": "array", "items": {"$ref": "#/components/schemas/Customer"}}, "casePermissionRequest": {"$ref": "#/components/schemas/CasePermissionRequest"}}}, "CriteriaRequest": {"type": "object", "properties": {"type": {"type": "string"}, "question": {"type": "string"}}}, "DuplicateBusinessCaseRequest": {"type": "object", "properties": {"businessCaseType": {"type": "string", "enum": ["FINANCING_CASE", "PASSING_CASE"]}, "state": {"type": "string", "enum": ["ACTIVE_PRIVATE", "ACTIVE_PUBLIC", "INACTIVE_CANCELLED", "INACTIVE_COMPLETED"]}, "companyId": {"type": "string"}, "templateId": {"type": "string"}, "minParticipationAmount": {"type": "number", "format": "double"}, "maxParticipationAmount": {"type": "number", "format": "double"}, "linkCADR": {"type": "boolean"}, "portalUsers": {"type": "array", "items": {"type": "string"}}, "participants": {"type": "array", "items": {"$ref": "#/components/schemas/ParticipantCustomer"}}, "invitees": {"type": "array", "items": {"$ref": "#/components/schemas/InviteCustomerRequest"}}, "contactPersonIds": {"type": "array", "items": {"type": "string"}}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}, "finStructureParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}, "facilityParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}, "copyInformationForFieldKeys": {"type": "array", "items": {"type": "string"}}, "groupsOrdered": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}, "inboxDocuments": {"type": "array", "items": {"type": "string"}}, "faq": {"type": "array", "items": {"$ref": "#/components/schemas/Faq"}}, "criteria": {"type": "array", "items": {"$ref": "#/components/schemas/CriteriaRequest"}}, "persistAllInformation": {"type": "boolean"}, "persistFinancingStructure": {"type": "boolean"}}}, "InviteCustomerRequest": {"type": "object", "properties": {"legalName": {"type": "string"}, "customerKey": {"type": "string"}, "bic": {"type": "string"}, "isRegistered": {"type": "boolean"}, "persons": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "participationType": {"type": "string"}, "customerType": {"type": "string", "enum": ["BANK", "REAL_ESTATE", "CORPORATE", "INTERNAL", "FSP"]}, "customerStatus": {"type": "string", "enum": ["REGULAR", "GUEST"]}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "userType": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "enabled": {"type": "boolean"}, "email": {"type": "string"}}}, "BulkOperationResultInvitationResult": {"type": "object", "properties": {"successful": {"type": "array", "items": {"$ref": "#/components/schemas/InvitationResult"}}, "failed": {"type": "array", "items": {"$ref": "#/components/schemas/InvitationResult"}}}}, "DuplicateBusinessCaseResponse": {"type": "object", "properties": {"createdBusinessCase": {"$ref": "#/components/schemas/BusinessCase"}, "hasError": {"type": "boolean"}, "evaluationFailures": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationFailureReason"}}, "failedAddingFAQ": {"type": "boolean"}, "failedAddingCriteria": {"type": "boolean"}, "failedCreatingInvitation": {"type": "boolean"}, "failedDuplicatingPermissionSets": {"type": "boolean"}, "invitationResults": {"$ref": "#/components/schemas/BulkOperationResultInvitationResult"}, "failedInformation": {"type": "object", "additionalProperties": {"type": "string"}}, "failedInboxDocuments": {"type": "object", "additionalProperties": {"type": "string"}}, "failedPortalAssignments": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Invitation": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "subBusinessCaseId": {"type": "string"}, "businessCaseId": {"type": "string"}, "autoGeneratedBusinessCaseName": {"type": "string"}, "callerCustomerKey": {"type": "string"}, "invitedCustomerKey": {"type": "string"}, "customerType": {"type": "string", "enum": ["BANK", "REAL_ESTATE", "CORPORATE", "INTERNAL", "FSP"]}, "invitationStatus": {"type": "string", "enum": ["PENDING", "ACCEPTED", "EXPIRED", "CANCELED", "DECLINED", "REMOVED_FROM_BUSINESS_CASE"]}, "chatId": {"type": "string"}, "invitedUsers": {"type": "array", "items": {"$ref": "#/components/schemas/InvitedUser"}}, "applicationId": {"type": "string"}, "ndaContractId": {"type": "string"}, "participationType": {"type": "string"}}}, "InvitationResult": {"type": "object", "properties": {"legalName": {"type": "string"}, "invitation": {"$ref": "#/components/schemas/Invitation"}, "status": {"type": "string", "enum": ["SUCCESSFUL", "FAILED"]}, "failureReason": {"type": "string"}}}, "InvitedUser": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "customerKey": {"type": "string"}, "explicitlyInvited": {"type": "boolean"}}}, "BusinessCaseData": {"type": "object", "properties": {"businessCase": {"$ref": "#/components/schemas/BusinessCase"}, "financingStructures": {"type": "array", "items": {"$ref": "#/components/schemas/FinancingStructure"}}, "finStructureSharingEntities": {"type": "array", "items": {"$ref": "#/components/schemas/FinStructureSharingEntity"}}, "participantCasePermissionSet": {"type": "array", "items": {"$ref": "#/components/schemas/ParticipantCasePermissionSetEntity"}}, "chats": {"type": "array", "items": {"$ref": "#/components/schemas/Chat"}}, "information": {"type": "array", "items": {"$ref": "#/components/schemas/InformationData"}}, "faq": {"type": "array", "items": {"$ref": "#/components/schemas/CreateFaqRequest"}}, "criteria": {"type": "array", "items": {"$ref": "#/components/schemas/CriteriaRequest"}}, "companyData": {"$ref": "#/components/schemas/CompanyData"}, "userCases": {"type": "array", "items": {"$ref": "#/components/schemas/UserCaseDto"}}, "inboxDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}, "templates": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "fieldHistories": {"type": "array", "items": {"$ref": "#/components/schemas/FieldHistory"}}, "kpis": {"type": "array", "items": {"$ref": "#/components/schemas/BusinessCaseKpi"}}, "kpiComments": {"type": "array", "items": {"$ref": "#/components/schemas/KpiComment"}}}}, "BusinessCaseKpi": {"type": "object", "properties": {"key": {"type": "string"}, "customerKey": {"type": "string"}, "businessCaseId": {"type": "string"}, "displayableFormula": {"type": "string"}, "formula": {"type": "string"}, "rangeDirection": {"type": "string", "enum": ["ASC", "DESC"]}, "type": {"type": "string", "enum": ["MONETARY", "DECIMAL", "PERCENTAGE"]}, "cluster": {"type": "string", "enum": ["RELATIONS", "RETURNS", "COSTS", "MULTIPLIERS", "DEBT_SERVICE", "OTHERS"]}, "orderIndex": {"type": "integer", "format": "int32"}, "enabled": {"type": "boolean"}, "checkExclusionCriteriaRange": {"$ref": "#/components/schemas/KpiRange"}, "checkPermissibleDeviationRange": {"$ref": "#/components/schemas/KpiRange"}, "compliantWithSpecificationsRange": {"$ref": "#/components/schemas/KpiRange"}, "riskEvaluationStatus": {"type": "string", "enum": ["CHECK_EXCLUSION_CRITERIA", "CHECK_PERMISSIBLE_DEVIATION", "COMPLIANT_WITH_SPECIFICATIONS", "NOT_DEFINED"]}, "value": {"type": "number", "format": "double"}, "id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "businessCaseActive": {"type": "boolean"}}}, "CADRGroup": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}, "fields": {"type": "array", "items": {"type": "string"}}, "rootFolder": {"$ref": "#/components/schemas/Folder"}, "groupVisibility": {"$ref": "#/components/schemas/GroupVisibility"}, "visibility": {"type": "string", "deprecated": true, "enum": ["PUBLIC", "PRIVATE", "PARTICIPANT", "INVITEE_AND_APPLICANT"]}}}, "CADRShareObject": {"type": "object", "properties": {"customerKey": {"type": "string"}, "companyId": {"type": "string"}, "type": {"type": "string", "enum": ["EXPLICIT", "IMPLICIT"]}}}, "CADRTemplate": {"type": "object", "properties": {"customerKey": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}, "groupsOrdered": {"type": "array", "items": {"$ref": "#/components/schemas/CADRGroup"}}, "version": {"type": "integer", "format": "int32"}, "versionDescription": {"type": "string"}, "isLatest": {"type": "boolean"}}}, "Chat": {"type": "object", "properties": {"id": {"type": "string"}, "businessCaseId": {"type": "string"}, "topic": {"type": "string"}, "component": {"type": "string"}, "applicationId": {"type": "string"}, "invitationId": {"type": "string"}, "toCustomer": {"type": "string"}, "userId": {"type": "string"}, "customerKey": {"type": "string"}, "createdFromButton": {"type": "boolean"}, "toUserId": {"type": "string"}, "description": {"type": "string"}, "archiveOperation": {"type": "string", "enum": ["REMOVE_PARTICIPANT_AUTOMATIC_ARCHIVE", "TRANSFER_LEADERSHIP_AUTOMATIC_ARCHIVE", "REJECT_APPLICATION_AUTOMATIC_ARCHIVE", "DECLINE_INVITATION_AUTOMATIC_ARCHIVE", "CANCEL_INVITATION_AUTOMATIC_ARCHIVE", "MANUAL_ARCHIVE"]}, "chatType": {"type": "string", "enum": ["INTERNAL", "ON_TOPIC", "ONE_ON_ONE", "CONSORTIUM", "INTERNAL_BILATERAL", "INTERNAL_GROUP"]}, "status": {"type": "string", "enum": ["ACTIVE", "ARCHIVED"]}, "chatUsers": {"type": "array", "items": {"$ref": "#/components/schemas/ChatUser"}}, "chatCustomers": {"type": "array", "items": {"$ref": "#/components/schemas/ChatCustomer"}}, "archivedBy": {"type": "string"}, "archivedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "ChatCustomer": {"type": "object", "properties": {"id": {"type": "string"}, "customerKey": {"type": "string"}}}, "ChatUser": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "userType": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "enabled": {"type": "boolean"}, "email": {"type": "string"}, "customerKey": {"type": "string"}, "salutation": {"type": "string"}, "academicTitle": {"type": "string"}, "department": {"type": "string"}, "position": {"type": "string"}, "mobileNumber": {"type": "string"}, "landlineNumber": {"type": "string"}}}, "Company": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "customerKey": {"type": "string"}, "isClient": {"type": "boolean"}, "companyInfo": {"$ref": "#/components/schemas/CompanyInfo"}, "address": {"$ref": "#/components/schemas/Address"}, "companyTemplate": {"$ref": "#/components/schemas/CompanyTemplate"}, "companyState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "companyBranches": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyBranch"}}}}, "CompanyBranch": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "branchType": {"type": "string"}, "address": {"$ref": "#/components/schemas/CompanyBranchAddress"}}}, "CompanyBranchAddress": {"type": "object", "properties": {"street": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "coordinates": {"$ref": "#/components/schemas/CompanyBranchCoordinates"}}}, "CompanyBranchCoordinates": {"type": "object", "properties": {"lat": {"type": "string"}, "lng": {"type": "string"}}}, "CompanyData": {"type": "object", "properties": {"company": {"$ref": "#/components/schemas/Company"}, "companyContactPersons": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "information": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyInformation"}}, "cadrTemplate": {"$ref": "#/components/schemas/CADRTemplate"}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}}}, "CompanyInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "legalName": {"type": "string"}, "legalForm": {"type": "string"}, "register": {"$ref": "#/components/schemas/Register"}, "industryInfo": {"$ref": "#/components/schemas/IndustryInfo"}, "foundingDate": {"type": "string", "format": "date"}, "taxId": {"type": "string"}, "registeredOn": {"type": "string", "format": "date"}, "extractFrom": {"type": "string", "format": "date"}, "taxNumber": {"type": "string"}, "vatId": {"type": "string"}}}, "CompanyInformation": {"required": ["companyId", "key", "value"], "type": "object", "properties": {"id": {"type": "string"}, "key": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "value": {"type": "object"}, "field": {"$ref": "#/components/schemas/FieldDto"}, "isPublic": {"type": "boolean"}, "informationState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "CompanyTemplate": {"type": "object", "properties": {"template": {"$ref": "#/components/schemas/CADRTemplate"}, "isCustom": {"type": "boolean"}, "isLocked": {"type": "boolean"}, "masterTemplateId": {"type": "string"}, "shareObjects": {"type": "array", "items": {"$ref": "#/components/schemas/CADRShareObject"}}}}, "ContentReference": {"type": "object", "properties": {"contentReferenceId": {"type": "string"}, "bucketName": {"type": "string"}, "documentKey": {"type": "string"}, "fileName": {"type": "string"}, "contentType": {"type": "string"}, "contentSize": {"type": "integer", "format": "int64"}, "isIndexed": {"type": "boolean"}, "isNotIndexable": {"type": "boolean"}, "indexRetryCount": {"type": "integer", "format": "int32"}}}, "CustomerData": {"type": "object", "properties": {"customer": {"$ref": "#/components/schemas/Customer"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "kpis": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerKpi"}}}}, "CustomerKpi": {"type": "object", "properties": {"key": {"type": "string"}, "customerKey": {"type": "string"}, "displayableFormula": {"type": "string"}, "formula": {"type": "string"}, "rangeDirection": {"type": "string", "enum": ["ASC", "DESC"]}, "type": {"type": "string", "enum": ["MONETARY", "DECIMAL", "PERCENTAGE"]}, "cluster": {"type": "string", "enum": ["RELATIONS", "RETURNS", "COSTS", "MULTIPLIERS", "DEBT_SERVICE", "OTHERS"]}, "orderIndex": {"type": "integer", "format": "int32"}, "enabled": {"type": "boolean"}, "checkExclusionCriteriaRange": {"$ref": "#/components/schemas/KpiRange"}, "checkPermissibleDeviationRange": {"$ref": "#/components/schemas/KpiRange"}, "compliantWithSpecificationsRange": {"$ref": "#/components/schemas/KpiRange"}, "id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "DeployBusinessCaseRequest": {"type": "object", "properties": {"businessCaseData": {"$ref": "#/components/schemas/BusinessCaseData"}, "customers": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerData"}}}}, "Document": {"type": "object", "properties": {"id": {"type": "string"}, "createdOn": {"type": "string"}, "realmKey": {"type": "string"}, "ownerReference": {"$ref": "#/components/schemas/OwnerReference"}, "businessCaseId": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "userCustomerKey": {"type": "string"}, "state": {"type": "string", "enum": ["DELETED", "TEMPORARY", "CONTENT_UPLOAD_ONGOING", "CONTENT_UPLOAD_FAILED", "FINAL"]}, "contentReference": {"$ref": "#/components/schemas/ContentReference"}, "contentBase64": {"type": "string"}, "digest": {"type": "string"}, "hasError": {"type": "boolean"}, "isContainedInInbox": {"type": "boolean"}, "isContainedInBusinessCase": {"type": "boolean"}, "isWarningIgnored": {"type": "boolean"}}}, "FinStructureField": {"type": "object", "properties": {"key": {"type": "string"}, "fieldType": {"type": "string", "enum": ["SHORT_TEXT", "LONG_TEXT", "INTEGER", "DECIMAL", "MONETARY", "DATE", "DOCUMENT", "MONTHS", "PERCENT", "BOOLEAN", "LOCATION", "SELECT", "TABLE", "DATE_RANGE", "COMPOSITE", "MULTI_SELECT"]}, "isRequired": {"type": "boolean"}, "isPublic": {"type": "boolean"}, "isPromoted": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "label": {"type": "string"}, "visibilityExpression": {"type": "string"}, "expression": {"type": "string"}, "value": {"type": "object"}, "description": {"type": "string"}, "isHidden": {"type": "boolean"}, "fieldMetaData": {"type": "object"}, "fieldOwner": {"$ref": "#/components/schemas/FieldOwner"}, "categoryId": {"type": "string"}, "portalVisibility": {"type": "string", "enum": ["VISIBLE", "REQUESTED", "NOT_SET"]}, "dependantFields": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "disabled": {"type": "boolean"}, "group": {"type": "string"}, "assetType": {"type": "string", "enum": ["HOTEL", "OTHER"]}, "ordinal": {"type": "integer", "format": "int32"}, "dynamic": {"type": "boolean"}, "restoredFromRevision": {"type": "boolean"}, "restoredFromRevisionId": {"type": "string"}, "isMultiSelect": {"type": "boolean", "deprecated": true}, "isReadOnly": {"type": "boolean"}, "fieldSetName": {"type": "string"}, "defaultInformation": {"type": "string"}}}, "FinStructureGroup": {"type": "object", "properties": {"id": {"type": "string"}, "group": {"type": "string"}, "key": {"type": "string"}, "ordinal": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["APPRAISAL_AREA", "BANK_AREA", "OBJECT_AREA"]}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FinStructureField"}}, "fieldsetName": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "FinStructureSharingEntity": {"type": "object", "properties": {"type": {"type": "string", "enum": ["SNAPSHOT", "LIVE"]}, "businessCaseId": {"type": "string"}, "finStructureId": {"type": "string"}, "fromCustomerKey": {"type": "string"}, "toCustomerKey": {"type": "string"}, "finStructure": {"$ref": "#/components/schemas/FinancingStructure"}, "sharedGroups": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "FinancingStructure": {"type": "object", "properties": {"id": {"type": "string"}, "ownerCustomerKey": {"type": "string"}, "businessCaseId": {"type": "string"}, "staticGroups": {"type": "array", "items": {"$ref": "#/components/schemas/FinStructureGroup"}}, "dynamicFieldsets": {"type": "array", "items": {"$ref": "#/components/schemas/FinStructureGroup"}}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "IndustryInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "industry": {"type": "string"}, "sector": {"type": "string"}}}, "InformationData": {"type": "object", "properties": {"information": {"$ref": "#/components/schemas/Information"}, "revisions": {"type": "array", "items": {"$ref": "#/components/schemas/InformationRevision"}}}}, "InformationRevision": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "businessCaseId": {"type": "string"}, "informationIdentifierKey": {"type": "string"}, "userId": {"type": "string"}, "information": {"$ref": "#/components/schemas/Information"}, "informationRevisionState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "masterRevisionId": {"type": "string"}}}, "KpiComment": {"type": "object", "properties": {"customerKey": {"type": "string"}, "businessCaseId": {"type": "string"}, "comment": {"type": "string"}, "kpiCommentAuthor": {"$ref": "#/components/schemas/KpiCommentAuthor"}, "id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "KpiCommentAuthor": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "username": {"type": "string"}}}, "KpiRange": {"type": "object", "properties": {"min": {"type": "number", "format": "double"}, "max": {"type": "number", "format": "double"}}}, "OwnerReference": {"type": "object", "properties": {"id": {"type": "string"}, "chatId": {"type": "string"}, "uploaderCustomerKey": {"type": "string"}, "businessCaseId": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "documentType": {"type": "string", "enum": ["COMMUNICATION", "CONTRACT", "BUSINESS_CASE", "INBOX", "BASE", "COMPANY", "EMAIL_ATTACHMENT", "PROFILE_PICTURE", "ORGANIZATION_LOGO"]}}}, "Register": {"type": "object", "properties": {"uuid": {"type": "string", "format": "uuid"}, "city": {"type": "string"}, "id": {"type": "string"}, "uniqueKey": {"type": "string"}}}, "UserCaseDto": {"type": "object", "properties": {"userId": {"type": "string"}, "caseId": {"type": "string"}}}, "CreateBusinessCaseRequest": {"type": "object", "properties": {"templateId": {"type": "string"}, "companyId": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}, "structuredFinancingConfiguration": {"$ref": "#/components/schemas/StructuredFinancingConfiguration"}, "businessCaseType": {"type": "string", "enum": ["FINANCING_CASE", "PASSING_CASE"]}, "facilityParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}, "finStructureParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}}}, "BusinessCaseResponse": {"type": "object", "properties": {"createdBusinessCase": {"$ref": "#/components/schemas/BusinessCase"}, "hasError": {"type": "boolean"}, "evaluationFailures": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationFailureReason"}}}}, "CreateFolderRequest": {"required": ["name", "parentFolderId"], "type": "object", "properties": {"fieldKeysToBeIncluded": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "parentFolderId": {"type": "string"}, "ordinal": {"type": "integer", "format": "int64"}}}, "FieldComparison": {"type": "object", "properties": {"fieldKey": {"type": "string"}, "valueComparisonEnabled": {"type": "boolean"}, "expectedValue": {"type": "object"}}}, "FolderComparison": {"type": "object", "properties": {"folderId": {"type": "string"}, "subfoldersComparisonEnabled": {"type": "boolean"}}}, "FolderStructureComparisonRequest": {"type": "object", "properties": {"template": {"$ref": "#/components/schemas/Template"}, "folderComparisons": {"type": "array", "items": {"$ref": "#/components/schemas/FolderComparison"}}, "fieldComparisons": {"type": "array", "items": {"$ref": "#/components/schemas/FieldComparison"}}}}, "AddFieldToFolderRequest": {"required": ["<PERSON><PERSON><PERSON>", "folderId"], "type": "object", "properties": {"fieldKeysToBeIncluded": {"type": "array", "items": {"type": "string"}}, "fieldKey": {"type": "string"}, "folderId": {"type": "string"}}}, "TemplateHistory": {"type": "object", "properties": {"templateId": {"type": "string"}, "version": {"type": "integer", "format": "int32"}, "versionDescription": {"type": "string"}}}, "TemplateVersionHistoryResponse": {"type": "object", "properties": {"templateName": {"type": "string"}, "numberOfRevisions": {"type": "integer", "format": "int32"}, "history": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateHistory"}}}}, "ParticipantCasePermissionSet": {"type": "object", "properties": {"businessCaseId": {"type": "string"}, "customerKey": {"type": "string"}, "name": {"type": "string"}, "appliedOnState": {"type": "string", "enum": ["INTERESTED", "COLLABORATOR", "PARTICIPANT", "LEADER"]}, "participationType": {"type": "string"}, "permissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "ReloadContextRequest": {"type": "object", "properties": {"businessCase": {"$ref": "#/components/schemas/BusinessCase"}, "participantCasePermissionSetList": {"type": "array", "items": {"$ref": "#/components/schemas/ParticipantCasePermissionSet"}}}}, "ParticipantVisibilityDTO": {"type": "object", "properties": {"businessCaseId": {"type": "string"}, "areAllParticipantsVisible": {"type": "boolean"}}}, "PageBusinessCase": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/BusinessCase"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "paged": {"type": "boolean"}, "unpaged": {"type": "boolean"}, "pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}}}, "SortObject": {"type": "object", "properties": {"direction": {"type": "string"}, "nullHandling": {"type": "string"}, "ascending": {"type": "boolean"}, "property": {"type": "string"}, "ignoreCase": {"type": "boolean"}}}, "FacilityRevision": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "businessCaseId": {"type": "string"}, "facilityName": {"type": "string"}, "facilityFieldKey": {"type": "string"}, "userId": {"type": "string"}, "facilityField": {"$ref": "#/components/schemas/FacilityField"}, "revisionState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}}}, "BusinessCaseStateInformation": {"type": "object", "properties": {"isInCompletableState": {"type": "boolean"}, "isInCancellableState": {"type": "boolean"}, "isInUnpublishableState": {"type": "boolean"}, "isInPublishableState": {"type": "boolean"}, "isInHideableState": {"type": "boolean"}, "isInReactivatableState": {"type": "boolean"}, "businessCaseId": {"type": "string"}}}, "CurrentLeadResponse": {"type": "object", "properties": {"leadCustomerKey": {"type": "string"}}}, "DeleteFolderRequest": {"required": ["folderGroup", "folderId"], "type": "object", "properties": {"fieldKeysToBeIncluded": {"type": "array", "items": {"type": "string"}}, "folderId": {"type": "string"}, "folderGroup": {"$ref": "#/components/schemas/Group"}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "name": "<PERSON><PERSON><PERSON>", "scheme": "bearer", "bearerFormat": "JWT"}}}}