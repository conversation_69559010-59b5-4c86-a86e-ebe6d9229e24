{"root": true, "ignorePatterns": ["!**/*"], "plugins": ["@nx"], "overrides": [{"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {}}, {"files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"], "env": {"jest": true}, "rules": {}}, {"files": ["*.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/dependency-checks": "error"}}, {"files": ["./package.json", "./generators.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/nx-plugin-checks": "error"}}]}