{"name": "e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "e2e/src", "implicitDependencies": ["fin-nx-plugins"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "e2e/jest.config.ts", "runInBand": true}, "dependsOn": ["^build"]}, "lint": {"executor": "@nx/eslint:lint"}}}