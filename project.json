{"name": "fin-nx-plugins", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}/fin-nx-plugins", "currentVersionResolver": "git-tag"}}}, "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/fin-nx-plugins", "main": "./src/index.ts", "tsConfig": "./tsconfig.lib.json", "assets": ["*.md", {"input": "./src", "glob": "**/!(*.ts)", "output": "./src"}, {"input": "./src", "glob": "**/*.d.ts", "output": "./src"}, {"input": ".", "glob": "generators.json", "output": "."}, {"input": ".", "glob": "executors.json", "output": "."}]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectName}"}}, "local-registry": {"executor": "@nx/js:verda<PERSON>o", "options": {"port": 4873, "config": ".verdaccio/config.yml", "storage": "tmp/local-registry/storage"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["./src", "{projectRoot}/package.json", "./package.json", "generators.json"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "jest.config.ts"}}}}