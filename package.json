{"name": "@fincloud/nx-plugin", "version": "0.0.1", "license": "MIT", "scripts": {}, "dependencies": {"@gitbeaker/rest": "43.0.0", "@nx/devkit": "19.0.8", "tslib": "^2.3.0"}, "devDependencies": {"@angular-devkit/core": "~17.3.0", "@nx/angular": "^19.0.8", "@nx/eslint": "19.0.8", "@nx/eslint-plugin": "19.0.8", "@nx/jest": "19.0.8", "@nx/js": "19.0.8", "@nx/plugin": "19.0.8", "@nx/workspace": "19.0.8", "@swc-node/register": "~1.8.0", "@swc/cli": "~0.3.12", "@swc/core": "~1.3.85", "@swc/helpers": "~0.5.2", "@types/adm-zip": "0.5.7", "@types/jest": "^29.4.0", "@types/node": "18.16.9", "@typescript-eslint/eslint-plugin": "^7.3.0", "@typescript-eslint/parser": "^7.3.0", "adm-zip": "0.5.16", "eslint": "~8.57.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "nx": "19.0.8", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "~5.4.2", "verdaccio": "^5.0.4"}, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "generators": "./generators.json"}